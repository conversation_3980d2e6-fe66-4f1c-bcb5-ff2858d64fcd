import { SeriesStatus, Series, CreateSeriesInput, UpdateSeriesInput } from '../../src/types/series';

// Test data generators
export class TestDataFactory {
  private static counter = 0;

  static getUniqueId(): string {
    this.counter++;
    return `test-id-${this.counter}-${Date.now()}`;
  }

  static createSeries(overrides: Partial<Series> = {}): Series {
    const id = this.getUniqueId();
    return {
      id,
      name: `Test Series ${id}`,
      chapter: Math.floor(Math.random() * 1000) + 1,
      status: this.getRandomStatus(),
      updated_at: new Date(),
      ...overrides,
    };
  }

  static createSeriesInput(overrides: Partial<CreateSeriesInput> = {}): CreateSeriesInput {
    return {
      name: `New Test Series ${this.getUniqueId()}`,
      chapter: Math.floor(Math.random() * 100) + 1,
      status: this.getRandomStatus(),
      ...overrides,
    };
  }

  static createUpdateInput(overrides: Partial<UpdateSeriesInput> = {}): UpdateSeriesInput {
    return {
      chapter: Math.floor(Math.random() * 1000) + 1,
      status: this.getRandomStatus(),
      ...overrides,
    };
  }

  static createMultipleSeries(count: number, overrides: Partial<Series> = {}): Series[] {
    return Array.from({ length: count }, (_, i) => 
      this.createSeries({ 
        name: `Bulk Series ${i + 1}`,
        chapter: i + 1,
        ...overrides 
      })
    );
  }

  static getRandomStatus(): typeof SeriesStatus[keyof typeof SeriesStatus] {
    const statuses = Object.values(SeriesStatus);
    return statuses[Math.floor(Math.random() * statuses.length)];
  }

  static createPaginatedResponse<T>(data: T[], page = 1, limit = 10) {
    const total = data.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = data.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }
}

// Predefined test scenarios
export const TestScenarios = {
  // Valid series data
  validSeries: {
    minimal: {
      name: 'Minimal Test Series',
    },
    complete: {
      name: 'Complete Test Series',
      chapter: 150,
      status: SeriesStatus.READING,
    },
    withNulls: {
      name: 'Series with Nulls',
      chapter: null,
      status: null,
    },
  },

  // Invalid series data
  invalidSeries: {
    emptyName: {
      name: '',
      chapter: 100,
      status: SeriesStatus.READING,
    },
    longName: {
      name: 'A'.repeat(256), // Over 255 character limit
      chapter: 100,
      status: SeriesStatus.READING,
    },
    negativeChapter: {
      name: 'Negative Chapter Series',
      chapter: -5,
      status: SeriesStatus.READING,
    },
    invalidStatus: {
      name: 'Invalid Status Series',
      chapter: 100,
      status: 'InvalidStatus' as any,
    },
  },

  // Pagination scenarios
  pagination: {
    firstPage: { page: '1', limit: '10' },
    middlePage: { page: '5', limit: '20' },
    lastPage: { page: '10', limit: '10' },
    maxLimit: { page: '1', limit: '100' },
    invalidPage: { page: '0', limit: '10' },
    invalidLimit: { page: '1', limit: '101' },
  },

  // Sorting scenarios
  sorting: {
    nameAsc: { sort: 'name', order: 'asc' },
    nameDesc: { sort: 'name', order: 'desc' },
    chapterAsc: { sort: 'chapter', order: 'asc' },
    chapterDesc: { sort: 'chapter', order: 'desc' },
    statusAsc: { sort: 'status', order: 'asc' },
    statusDesc: { sort: 'status', order: 'desc' },
    updatedAsc: { sort: 'updated_at', order: 'asc' },
    updatedDesc: { sort: 'updated_at', order: 'desc' },
  },

  // Filtering scenarios
  filtering: {
    reading: { status: SeriesStatus.READING },
    completed: { status: SeriesStatus.COMPLETED },
    onHold: { status: SeriesStatus.ON_HOLD },
    dropped: { status: SeriesStatus.DROPPED },
    cancelled: { status: SeriesStatus.CANCELLED },
    planToRead: { status: SeriesStatus.PLAN_TO_READ },
  },

  // Search scenarios
  search: {
    simple: { search: 'naruto' },
    caseInsensitive: { search: 'NARUTO' },
    partial: { search: 'naru' },
    withSpaces: { search: 'one piece' },
    specialChars: { search: 'series-1' },
    unicode: { search: 'テスト' },
  },
};

// Mock database responses
export const MockResponses = {
  emptyList: {
    data: [],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
      hasNext: false,
      hasPrev: false,
    },
  },

  singleItem: (series: Series) => ({
    data: [series],
    pagination: {
      page: 1,
      limit: 10,
      total: 1,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    },
  }),

  multipleItems: (seriesList: Series[], page = 1, limit = 10) => 
    TestDataFactory.createPaginatedResponse(seriesList, page, limit),

  databaseError: new Error('Database connection failed'),
  validationError: new Error('Validation failed'),
  notFoundError: new Error('Series not found'),
  duplicateError: new Error('Series already exists'),
};

// Test utilities for common operations
export const TestUtils = {
  // Generate realistic series names
  generateSeriesName: (index?: number): string => {
    const prefixes = ['The', 'Chronicles of', 'Legend of', 'Tales of', 'Adventures of'];
    const nouns = ['Dragon', 'Phoenix', 'Warrior', 'Mage', 'Knight', 'Hero', 'Shadow'];
    const suffixes = ['Saga', 'Quest', 'Journey', 'Chronicles', 'Legacy', 'Destiny'];
    
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
    
    const name = `${prefix} ${noun} ${suffix}`;
    return index !== undefined ? `${name} ${index}` : name;
  },

  // Generate realistic chapter numbers
  generateChapterNumber: (status: string): number => {
    switch (status) {
      case SeriesStatus.PLAN_TO_READ:
        return 0;
      case SeriesStatus.READING:
        return Math.floor(Math.random() * 500) + 1;
      case SeriesStatus.COMPLETED:
        return Math.floor(Math.random() * 1000) + 100;
      case SeriesStatus.ON_HOLD:
        return Math.floor(Math.random() * 300) + 1;
      case SeriesStatus.DROPPED:
        return Math.floor(Math.random() * 100) + 1;
      case SeriesStatus.CANCELLED:
        return Math.floor(Math.random() * 200) + 1;
      default:
        return Math.floor(Math.random() * 100) + 1;
    }
  },

  // Create series with realistic data
  createRealisticSeries: (count: number): Series[] => {
    return Array.from({ length: count }, (_, i) => {
      const status = TestDataFactory.getRandomStatus();
      return TestDataFactory.createSeries({
        name: TestUtils.generateSeriesName(i + 1),
        chapter: TestUtils.generateChapterNumber(status),
        status,
        updated_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000), // Random date within last year
      });
    });
  },

  // Validate series object structure
  validateSeriesStructure: (series: any): boolean => {
    return (
      typeof series.id === 'string' &&
      typeof series.name === 'string' &&
      (typeof series.chapter === 'number' || series.chapter === null) &&
      (typeof series.status === 'string' || series.status === null) &&
      series.updated_at instanceof Date
    );
  },

  // Validate pagination structure
  validatePaginationStructure: (pagination: any): boolean => {
    return (
      typeof pagination.page === 'number' &&
      typeof pagination.limit === 'number' &&
      typeof pagination.total === 'number' &&
      typeof pagination.totalPages === 'number' &&
      typeof pagination.hasNext === 'boolean' &&
      typeof pagination.hasPrev === 'boolean'
    );
  },

  // Create test database service mock
  createMockDatabaseService: (customMethods: any = {}) => ({
    getAllSeries: jest.fn(),
    getSeriesById: jest.fn(),
    createSeries: jest.fn(),
    updateSeries: jest.fn(),
    deleteSeries: jest.fn(),
    seriesExists: jest.fn(),
    ...customMethods,
  }),

  // Create test Hono context mock
  createMockContext: (overrides: any = {}) => ({
    env: {
      DB: {
        prepare: jest.fn().mockReturnThis(),
        bind: jest.fn().mockReturnThis(),
        first: jest.fn(),
        all: jest.fn(),
        run: jest.fn(),
      },
      JWKS_URL: 'https://test-provider.com/.well-known/jwks.json',
      JWT_ISSUER: 'test-issuer',
      JWT_AUDIENCE: 'test-audience',
      ...overrides.env,
    },
    req: {
      json: jest.fn(),
      query: jest.fn(),
      param: jest.fn(),
      ...overrides.req,
    },
    json: jest.fn(),
    ...overrides,
  }),

  // Wait for async operations
  wait: (ms: number): Promise<void> => 
    new Promise(resolve => setTimeout(resolve, ms)),

  // Generate UUID-like test IDs
  generateTestId: (): string => 
    `test-${Math.random().toString(36).substr(2, 9)}-${Date.now()}`,
};
