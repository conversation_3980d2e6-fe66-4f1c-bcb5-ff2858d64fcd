# OAuth JWT Authentication with JW<PERSON> Implementation

This document describes the OAuth JWT authentication system implemented in the Series Management API using J<PERSON><PERSON> (JSON Web Key Set) for token verification.

## Overview

The API now requires JWT authentication for all endpoints except the health check. Each user's data is isolated based on the `sub` (subject) claim in the JW<PERSON> token, which serves as the user ID. The API uses your OAuth provider's <PERSON><PERSON><PERSON> endpoint to verify token signatures, eliminating the need for shared secrets.

## Architecture

### Authentication Flow

1. **Client obtains JWT token** from your OAuth provider
2. **Client includes token** in Authorization header: `Bearer <token>`
3. **API fetches JWKS** from your OAuth provider's `.well-known` endpoint (cached for 1 hour)
4. **API validates token signature** using the appropriate public key from JWKS
5. **API validates token claims** (expiration, issuer, audience)
6. **API extracts user ID** from the `sub` claim
7. **API filters all data** by the user ID

### Database Schema Changes

The `series` table now includes a `user_id` column:

```sql
CREATE TABLE IF NOT EXISTS series (
    id TEXT PRIMARY KEY DEFAULT (...),
    user_id TEXT NOT NULL,           -- New: User isolation
    name TEXT NOT NULL,
    chapter REAL,
    status TEXT CHECK (...),
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, name)           -- New: Unique per user
);
```

## Configuration

### Environment Variables

Set these in your Cloudflare Workers environment:

- `JWKS_URL` (required): Your OAuth provider's JWKS endpoint URL
- `JWT_ISSUER` (optional): Expected issuer claim
- `JWT_AUDIENCE` (optional): Expected audience claim

### Local Development (.dev.vars)

```env
JWKS_URL=https://your-oauth-provider.com/.well-known/jwks.json
JWT_ISSUER=your-oauth-provider
JWT_AUDIENCE=novel-archives-api
```

### Production (Cloudflare Workers)

```bash
wrangler secret put JWKS_URL
wrangler secret put JWT_ISSUER
wrangler secret put JWT_AUDIENCE
```

### Common JWKS URLs

- **Auth0**: `https://your-domain.auth0.com/.well-known/jwks.json`
- **Google**: `https://www.googleapis.com/oauth2/v3/certs`
- **Microsoft**: `https://login.microsoftonline.com/common/discovery/v2.0/keys`
- **Okta**: `https://your-domain.okta.com/oauth2/default/v1/keys`
- **Firebase**: `https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>`

## JWT Requirements

### Supported Algorithms

- **RS256**: RSA Signature with SHA-256
- **ES256**: ECDSA using P-256 and SHA-256

### Required Claims

- `sub`: User ID (string) - used for data isolation

### Optional Claims

- `iss`: Issuer - must match `JWT_ISSUER` if configured
- `aud`: Audience - must match `JWT_AUDIENCE` if configured
- `exp`: Expiration time (automatically validated)
- `iat`: Issued at time
- `kid`: Key ID (used to find the correct public key in JWKS)

### Example JWT Payload

```json
{
  "sub": "user-123",
  "iss": "your-oauth-provider",
  "aud": "novel-archives-api",
  "exp": **********,
  "iat": **********
}
```

## API Usage

### Authentication Header

All protected endpoints require:

```
Authorization: Bearer <your-jwt-token>
```

### Example Request

```bash
curl -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
     https://your-api.workers.dev/api/series
```

## Data Isolation

- Each user only sees their own series
- Series names must be unique per user (not globally)
- All CRUD operations are automatically filtered by user ID
- No user can access another user's data

## Error Responses

### 401 Unauthorized

```json
{
  "success": false,
  "error": "Authentication required",
  "message": "Authorization header is missing"
}
```

### 403 Forbidden

```json
{
  "success": false,
  "error": "Invalid token",
  "message": "The provided token is invalid or expired"
}
```

## Testing

### Generate Test JWT

Use the provided test script:

```bash
npm run test-auth
```

This generates a test JWT token and makes a request to verify authentication.

### Manual Testing

1. Generate a JWT token with your OAuth provider
2. Include it in the Authorization header
3. Make requests to protected endpoints

## Migration

### Existing Data

If you have existing data without user IDs, run the migration:

```bash
npm run migrate-d1 your-database-name
```

This will:
1. Add the `user_id` column
2. Update the unique constraint
3. Assign existing data to a default user (you may want to modify this)

### Breaking Changes

- All API endpoints now require authentication
- Database schema has changed (added `user_id` column)
- Series names are now unique per user, not globally

## Security Considerations

- Use a strong, random JWT secret in production
- Ensure JWT tokens have appropriate expiration times
- Validate issuer and audience claims if using multiple services
- Consider implementing token refresh mechanisms
- Monitor for suspicious authentication patterns

## Troubleshooting

### Common Issues

1. **"JWT_SECRET environment variable is not set"**
   - Ensure JWT_SECRET is configured in your environment

2. **"Invalid token format"**
   - Check Authorization header format: `Bearer <token>`

3. **"The token does not contain required claims"**
   - Ensure JWT has a `sub` claim with the user ID

4. **"Series not found" for existing data**
   - User may not have access to that series (different user_id)
   - Check if migration was run correctly
