# PowerShell schema migration script for existing Cloudflare D1 database
# This script runs the schema migration on your existing D1 database

param(
    [Parameter(Mandatory=$true)]
    [string]$DatabaseName
)

Write-Host "Running schema migration on existing Cloudflare D1 database..." -ForegroundColor Green

# Check if wrangler is installed
try {
    $null = Get-Command wrangler -ErrorAction Stop
} catch {
    Write-Host "Error: Wrangler CLI is not installed. Please install it first:" -ForegroundColor Red
    Write-Host "npm install -g wrangler" -ForegroundColor Yellow
    exit 1
}

# Run schema migration on existing database
Write-Host "Running schema migration on database: $DatabaseName" -ForegroundColor Yellow
try {
    wrangler d1 execute $DatabaseName --file=./migrations/schema.sql --remote

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Schema migration completed successfully!" -ForegroundColor Green
    } else {
        throw "Schema migration failed"
    }
} catch {
    Write-Host "❌ Schema migration failed!" -ForegroundColor Red
    Write-Host "Make sure:" -ForegroundColor Yellow
    Write-Host "1. The database name '$DatabaseName' exists in your Cloudflare account" -ForegroundColor White
    Write-Host "2. The database is properly configured in wrangler.jsonc" -ForegroundColor White
    Write-Host "3. You are authenticated with Cloudflare (run 'wrangler auth login')" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "🎉 Schema migration complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Make sure your wrangler.jsonc has the correct database configuration" -ForegroundColor White
Write-Host "2. Run 'npm run deploy' to deploy your Worker with D1 binding" -ForegroundColor White
