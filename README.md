# Series Management API

A RESTful API built with Hono.js for managing a series database with CRUD operations and pagination support.

## Features

- ✅ Full CRUD operations (Create, Read, Update, Delete)
- ✅ OAuth JWT Authentication with user-based authorization
- ✅ User-isolated data (each user only sees their own series)
- ✅ Pagination with customizable page size
- ✅ Filtering by status and search by name
- ✅ Sorting by multiple fields (name, chapter, status, updated_at)
- ✅ Input validation with Zod schemas
- ✅ Cloudflare D1 database integration
- ✅ TypeScript support
- ✅ Error handling and proper HTTP status codes
- ✅ CORS support

## Database Schema

The API works with a Cloudflare D1 (SQLite) table called `series` with user-based isolation:

```sql
CREATE TABLE IF NOT EXISTS series (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    chapter REAL,
    status TEXT CHECK (status IN ('Reading', 'Completed', 'On-Hold', 'Dropped', 'Cancelled', 'Plan to Read')),
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, name) -- Each user can have unique series names
);
```

## Setup

1. Install dependencies:
```bash
npm install
```

2. Configure your existing Cloudflare D1 database in `wrangler.jsonc`:
```json
{
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "your-existing-database-name",
      "database_id": "your-existing-database-id"
    }
  ]
}
```

3. Run the schema migration on your existing D1 database:

**For Unix/Linux/macOS:**
```bash
npm run migrate-d1 your-database-name
```

**For Windows:**
```powershell
npm run migrate-d1:windows your-database-name
```

4. Configure JWT authentication using JWKS in your environment:

**For local development (.dev.vars):**
```env
JWKS_URL=https://your-oauth-provider.com/.well-known/jwks.json
JWT_ISSUER=your-oauth-provider
JWT_AUDIENCE=novel-archives-api
```

**For production (Cloudflare Workers environment variables):**
Set these in your Cloudflare Workers dashboard or via wrangler:
```bash
wrangler secret put JWKS_URL
wrangler secret put JWT_ISSUER
wrangler secret put JWT_AUDIENCE
```

**Common JWKS URLs:**
- Auth0: `https://your-domain.auth0.com/.well-known/jwks.json`
- Google: `https://www.googleapis.com/oauth2/v3/certs`
- Microsoft: `https://login.microsoftonline.com/common/discovery/v2.0/keys`
- Okta: `https://your-domain.okta.com/oauth2/default/v1/keys`

5. Deploy to Cloudflare Workers:
```bash
npm run deploy
```

## Authentication

All API endpoints (except health check) require JWT authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

The JWT token must contain a `sub` (subject) claim with the user ID. This user ID is used to isolate data between users.

### JWT Requirements

- **Algorithm**: Supports RS256 (RSA) and ES256 (ECDSA) algorithms
- **Verification**: Uses JWKS (JSON Web Key Set) from your OAuth provider's `.well-known` endpoint
- **Required Claims**:
  - `sub`: User ID (string) - used for data isolation
- **Optional Claims**:
  - `iss`: Issuer - must match `JWT_ISSUER` if configured
  - `aud`: Audience - must match `JWT_AUDIENCE` if configured
  - `exp`: Expiration time (automatically validated)
  - `iat`: Issued at time
  - `kid`: Key ID (used to find the correct public key in JWKS)

## API Endpoints

### Health Check
- **GET** `/` - API health check and endpoint documentation (no authentication required)

### Series Management (Authentication Required)

#### Get All Series (with pagination)
- **GET** `/api/series`

**Query Parameters:**
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10, max: 100) - Items per page
- `sort` (string, default: "updated_at") - Sort field: name, chapter, status, updated_at
- `order` (string, default: "desc") - Sort order: asc, desc
- `status` (string, optional) - Filter by status: Reading, Completed, On-Hold, Dropped, Cancelled, Plan to Read
- `search` (string, optional) - Search by series name (case-insensitive)

**Example:**
```bash
curl -H "Authorization: Bearer <your-jwt-token>" \
     "https://your-api.workers.dev/api/series?page=1&limit=20&sort=name&order=asc&status=Reading&search=naruto"
```

#### Get Series by ID
- **GET** `/api/series/:id`

#### Create New Series
- **POST** `/api/series`

**Request Body:**
```json
{
  "name": "Series Name",
  "chapter": 150,
  "status": "Reading"
}
```

#### Update Series
- **PUT** `/api/series/:id`

**Request Body (all fields optional):**
```json
{
  "name": "Updated Series Name",
  "chapter": 200,
  "status": "Completed"
}
```

#### Delete Series
- **DELETE** `/api/series/:id`

## Response Format

All API responses follow this format:

```json
{
  "success": boolean,
  "data": any,
  "error": string,
  "message": string
}
```

### Paginated Response Format

```json
{
  "success": true,
  "data": {
    "data": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## Status Values

The following status values are supported:
- `Reading`
- `Completed`
- `On-Hold`
- `Dropped`
- `Cancelled`
- `Plan to Read`

## Development

For generating/synchronizing types based on your Worker configuration:

```bash
npm run cf-typegen
```

## Error Handling

The API returns appropriate HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `404` - Not Found
- `409` - Conflict (duplicate name)
- `500` - Internal Server Error
