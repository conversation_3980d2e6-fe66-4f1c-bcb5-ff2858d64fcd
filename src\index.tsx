import { Hono } from "hono";
import { cors } from "hono/cors";
import { SeriesController } from "./controllers/series";
import { SeriesStatus } from "./types/series";
import { D1Database } from "@cloudflare/workers-types";
import { authMiddleware } from "./middleware/auth";

type CloudflareBindings = {
  DB: D1Database;
  JWKS_URL: string;
  JWT_ISSUER?: string;
  JWT_AUDIENCE?: string;
};

const app = new Hono<{ Bindings: CloudflareBindings }>();

// Add CORS middleware
app.use(
  "*",
  cors({
    origin: "http://localhost:5173",
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization"],
  })
);

app.get("init", async (c) => {
  const db = c.env.DB;
  const query = `
    CREATE TABLE IF NOT EXISTS series (
        id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(4))) || '-' || lower(hex(randomblob(2))) || '-4' || substr(lower(hex(randomblob(2))),2) || '-' || substr('89ab',abs(random()) % 4 + 1, 1) || substr(lower(hex(randomblob(2))),2) || '-' || lower(hex(randomblob(6)))),
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        chapter REAL,
        status TEXT CHECK (status IN ('Reading', 'Completed', 'On-Hold', 'Dropped', 'Cancelled', 'Plan to Read')),
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, name)
    );`;
  await db.prepare(query).run();

  return c.json({
    success: true,
    message: "API is running with authentication",
  });
});

// Initialize controller
const seriesController = new SeriesController();

// Health check endpoint
app.get("/", (c) => {
  return c.json({
    message: "Series API is running",
    version: "1.0.0",
    endpoints: {
      "GET /api/series": "List all series with pagination",
      "GET /api/series/:id": "Get series by ID",
      "POST /api/series": "Create new series",
      "PUT /api/series/:id": "Update series",
      "DELETE /api/series/:id": "Delete series",
      "GET /api/statuses": "Get available series statuses",
    },
  });
});

// API Routes - Protected with authentication
app.get("/api/series", authMiddleware, seriesController.getAllSeries);
app.get("/api/series/:id", authMiddleware, seriesController.getSeriesById);
app.post("/api/series", authMiddleware, seriesController.createSeries);
app.put("/api/series/:id", authMiddleware, seriesController.updateSeries);
app.delete("/api/series/:id", authMiddleware, seriesController.deleteSeries);

// Statuses endpoint
app.get("/api/statuses", (c) => {
  return c.json({
    success: true,
    data: Object.values(SeriesStatus),
  });
});

// 404 handler
app.notFound((c) => {
  return c.json(
    {
      success: false,
      error: "Endpoint not found",
      message: "The requested endpoint does not exist",
    },
    404
  );
});

// Error handler
app.onError((err, c) => {
  console.error("Unhandled error:", err);
  return c.json(
    {
      success: false,
      error: "Internal server error",
      message: "An unexpected error occurred",
    },
    500
  );
});

export default app;
