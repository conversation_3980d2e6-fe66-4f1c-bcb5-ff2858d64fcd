import { Context, Next } from "hono";
import { D1Database } from "@cloudflare/workers-types";
import { createRemoteJWKSet, jwtVerify } from "jose";
import { UserContext } from "src/types/auth";

type CloudflareBindings = {
  DB: D1Database;
  JWKS_URL: string;
  JWT_ISSUER?: string;
  JWT_AUDIENCE?: string;
};

export async function authMiddleware(
  context: Context<{ Bindings: CloudflareBindings }>,
  next: Next
): Promise<void | Response> {
  const audience = context.env.JWT_AUDIENCE;
  const issuer = context.env.JWT_ISSUER;

  if (!audience) {
    throw new Error(
      "OAuth2 audience is not defined. Please ensure an OAUTH_AUDIENCE is defined in your environment variables or its value passed in the configuration"
    );
  }
  if (!issuer) {
    throw new Error(
      "OAuth2 authority is not defined. Please ensure an OAUTH_AUTHORITY is defined in your environment variables or its value passed in the configuration"
    );
  }
  const authorization = context.req.header("Authorization");

  if (!authorization || !authorization.startsWith("Bearer ")) {
    return context.json({ message: "Unauthorized" }, 401);
  }
  const token = authorization.replace("Bearer ", "");

  const jwks = createRemoteJWKSet(new URL(`${issuer}.well-known/jwks.json`));

  console.log(new URL(`${issuer}.well-known/jwks.json`).toString());

  console.log(token)
  try {
    const { payload } = await jwtVerify(token, jwks, { audience, issuer });

    (context as any).user = {
      userId: payload.sub,
      ...payload,
    };
    await next();
  } catch (error) {
    console.log(error);
    return context.json({ message: "Invalid token" }, 403);
  }
}

export function getUserContext(c: Context): UserContext {
  const user = (c as any).user;
  if (!user) {
    throw new Error(
      "User context not found - ensure auth middleware is applied"
    );
  }
  return user;
}
