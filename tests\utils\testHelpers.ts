import { SeriesStatus } from '../../src/types/series';

// Test data factories
export const createMockSeries = (overrides: any = {}) => ({
  id: '123e4567-e89b-12d3-a456-426614174000',
  name: 'Test Series',
  chapter: 100,
  status: SeriesStatus.READING,
  updated_at: new Date('2024-01-01T00:00:00.000Z'),
  ...overrides,
});

export const createMockPaginatedResponse = (data: any[] = [], overrides: any = {}) => ({
  data,
  pagination: {
    page: 1,
    limit: 10,
    total: data.length,
    totalPages: Math.ceil(data.length / 10),
    hasNext: false,
    hasPrev: false,
    ...overrides.pagination,
  },
  ...overrides,
});

export const createMockCreateSeriesInput = (overrides: any = {}) => ({
  name: 'New Test Series',
  chapter: 1,
  status: SeriesStatus.READING,
  ...overrides,
});

export const createMockUpdateSeriesInput = (overrides: any = {}) => ({
  chapter: 150,
  status: SeriesStatus.COMPLETED,
  ...overrides,
});

// Mock Hono context factory
export const createMockHonoContext = (overrides: any = {}) => ({
  env: {
    DATABASE_URL: 'test://connection',
    ...overrides.env,
  },
  req: {
    json: jest.fn(),
    query: jest.fn(),
    param: jest.fn(),
    ...overrides.req,
  },
  json: jest.fn(),
  ...overrides,
});

// Test validation helpers
export const expectValidationError = (result: any, field?: string) => {
  expect(result.success).toBe(false);
  if (field) {
    expect(result.error.errors.some((err: any) => err.path.includes(field))).toBe(true);
  }
};

export const expectValidationSuccess = (result: any, expectedData?: any) => {
  expect(result.success).toBe(true);
  if (expectedData) {
    expect(result.data).toEqual(expectedData);
  }
};

// Database mock helpers
export const createMockDatabaseService = (overrides: any = {}) => ({
  getAllSeries: jest.fn(),
  getSeriesById: jest.fn(),
  createSeries: jest.fn(),
  updateSeries: jest.fn(),
  deleteSeries: jest.fn(),
  seriesExists: jest.fn(),
  ...overrides,
});

// Common test scenarios
export const testScenarios = {
  validSeries: createMockSeries(),
  validCreateInput: createMockCreateSeriesInput(),
  validUpdateInput: createMockUpdateSeriesInput(),
  
  invalidCreateInputs: [
    { name: '', description: 'empty name' },
    { name: 'Test', chapter: -5, description: 'negative chapter' },
    { name: 'Test', status: 'Invalid Status', description: 'invalid status' },
  ],
  
  invalidUpdateInputs: [
    { name: '', description: 'empty name' },
    { chapter: -10, description: 'negative chapter' },
    { status: 'Invalid Status', description: 'invalid status' },
  ],
  
  invalidPaginationInputs: [
    { page: '0', description: 'page less than 1' },
    { page: '-1', description: 'negative page' },
    { limit: '0', description: 'limit less than 1' },
    { limit: '101', description: 'limit over 100' },
    { sort: 'invalid_field', description: 'invalid sort field' },
    { order: 'invalid_order', description: 'invalid order' },
  ],
};

// Async test helpers
export const expectAsyncError = async (asyncFn: () => Promise<any>, expectedError?: string) => {
  try {
    await asyncFn();
    fail('Expected function to throw an error');
  } catch (error) {
    if (expectedError) {
      expect((error as Error).message).toContain(expectedError);
    }
  }
};

export const expectAsyncSuccess = async (asyncFn: () => Promise<any>, expectedResult?: any) => {
  const result = await asyncFn();
  if (expectedResult) {
    expect(result).toEqual(expectedResult);
  }
  return result;
};

// Response assertion helpers
export const expectSuccessResponse = (response: any, expectedData?: any) => {
  expect(response.success).toBe(true);
  if (expectedData) {
    expect(response.data).toEqual(expectedData);
  }
};

export const expectErrorResponse = (response: any, expectedError?: string, expectedStatus?: number) => {
  expect(response.success).toBe(false);
  if (expectedError) {
    expect(response.error).toContain(expectedError);
  }
};

// UUID validation helper
export const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

// Date validation helper
export const isValidDate = (date: any): boolean => {
  return date instanceof Date && !isNaN(date.getTime());
};

// Array assertion helpers
export const expectArrayLength = (array: any[], expectedLength: number) => {
  expect(Array.isArray(array)).toBe(true);
  expect(array).toHaveLength(expectedLength);
};

export const expectArrayContains = (array: any[], expectedItem: any) => {
  expect(Array.isArray(array)).toBe(true);
  expect(array).toContain(expectedItem);
};

// Pagination assertion helpers
export const expectValidPagination = (pagination: any, expectedValues?: any) => {
  expect(pagination).toHaveProperty('page');
  expect(pagination).toHaveProperty('limit');
  expect(pagination).toHaveProperty('total');
  expect(pagination).toHaveProperty('totalPages');
  expect(pagination).toHaveProperty('hasNext');
  expect(pagination).toHaveProperty('hasPrev');
  
  expect(typeof pagination.page).toBe('number');
  expect(typeof pagination.limit).toBe('number');
  expect(typeof pagination.total).toBe('number');
  expect(typeof pagination.totalPages).toBe('number');
  expect(typeof pagination.hasNext).toBe('boolean');
  expect(typeof pagination.hasPrev).toBe('boolean');
  
  expect(pagination.page).toBeGreaterThan(0);
  expect(pagination.limit).toBeGreaterThan(0);
  expect(pagination.total).toBeGreaterThanOrEqual(0);
  expect(pagination.totalPages).toBeGreaterThanOrEqual(0);
  
  if (expectedValues) {
    Object.keys(expectedValues).forEach(key => {
      expect(pagination[key]).toBe(expectedValues[key]);
    });
  }
};
