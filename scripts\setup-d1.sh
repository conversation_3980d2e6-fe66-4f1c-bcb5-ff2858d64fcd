#!/bin/bash

# Schema migration script for existing Cloudflare D1 database
# This script runs the schema migration on your existing D1 database

echo "Running schema migration on existing Cloudflare D1 database..."

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo "Error: Wrangler CLI is not installed. Please install it first:"
    echo "npm install -g wrangler"
    exit 1
fi

# Check if database name is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <database-name>"
    echo "Example: $0 series-db"
    echo ""
    echo "Make sure your database name matches what's configured in wrangler.jsonc"
    exit 1
fi

DB_NAME=$1

# Run schema migration on existing database
echo "Running schema migration on database: $DB_NAME"
wrangler d1 execute $DB_NAME --file=./migrations/schema.sql --remote

if [ $? -eq 0 ]; then
    echo "✅ Schema migration completed successfully!"
else
    echo "❌ Schema migration failed!"
    echo "Make sure:"
    echo "1. The database name '$DB_NAME' exists in your Cloudflare account"
    echo "2. The database is properly configured in wrangler.jsonc"
    echo "3. You are authenticated with Cloudflare (run 'wrangler auth login')"
    exit 1
fi

echo ""
echo "🎉 Schema migration complete!"
echo ""
echo "Next steps:"
echo "1. Make sure your wrangler.jsonc has the correct database configuration"
echo "2. Run 'npm run deploy' to deploy your Worker with D1 binding"
