{"name": "novel-archives-series-api", "type": "module", "scripts": {"start": "vite", "build": "vite build", "preview": "$npm_execpath run build && vite preview", "deploy": "$npm_execpath run build && wrangler deploy", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "migrate-d1": "bash scripts/setup-d1.sh", "migrate-d1:windows": "powershell -ExecutionPolicy Bypass -File scripts/setup-d1.ps1", "test-auth": "node scripts/test-auth.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/types tests/services tests/controllers", "test:integration": "jest tests/integration", "test:ci": "jest --coverage --watchAll=false"}, "dependencies": {"hono": "^4.7.11", "jose": "^6.0.11"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.2.3", "@cloudflare/workers-types": "^4.20250607.0", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "vite": "^6.3.5", "vite-ssr-components": "^0.2.0", "wrangler": "^4.17.0"}}