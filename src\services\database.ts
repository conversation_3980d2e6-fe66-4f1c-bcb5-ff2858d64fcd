import type {
  Series,
  CreateSeriesInput,
  UpdateSeriesInput,
  PaginationQuery,
  PaginatedResponse,
  SeriesStatusType,
} from "../types/series";

export class DatabaseService {
  private db: any; // D1Database type

  constructor(database: any) { // D1Database type
    this.db = database;
  }

  async getAllSeries(
    userId: string,
    query: PaginationQuery
  ): Promise<PaginatedResponse<Series>> {
    const { page, pageSize, sortBy, sortOrder, status, search } = query;
    const offset = (page - 1) * pageSize;

    // Build ORDER BY clause
    let orderColumn = 'updated_at';
    if (sortBy === 'name') orderColumn = 'name';
    else if (sortBy === 'chapter') orderColumn = 'chapter';
    else if (sortBy === 'status') orderColumn = 'status';
    else if (sortBy === 'updatedAt') orderColumn = 'updated_at';

    const orderDirection = sortOrder.toUpperCase();

    // Build WHERE clause and parameters - always filter by user_id
    let whereClause = 'WHERE user_id = ?';
    let params: any[] = [userId];

    if (status && search) {
      whereClause += ' AND status = ? AND name LIKE ?';
      params.push(status, `%${search}%`);
    } else if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    } else if (search) {
      whereClause += ' AND name LIKE ?';
      params.push(`%${search}%`);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as count FROM series ${whereClause}`;
    const countResult = await this.db.prepare(countQuery).bind(...params).first();
    const totalCount = countResult?.count || 0;

    // Get paginated data
    const dataQuery = `
      SELECT id, user_id, name, chapter, status, updated_at
      FROM series
      ${whereClause}
      ORDER BY ${orderColumn} ${orderDirection}
      LIMIT ? OFFSET ?
    `;
    const dataResult = await this.db.prepare(dataQuery).bind(...params, pageSize, offset).all();

    const totalPages = Math.ceil(totalCount / pageSize);

    return {
      data: dataResult.results.map((row: any) => ({
        id: row.id,
        userId: row.user_id,
        name: row.name,
        chapter: row.chapter,
        status: row.status,
        updatedAt: row.updated_at,
      })),
      totalCount,
      page,
      pageSize,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async getSeriesById(userId: string, id: string): Promise<Series | null> {
    const result = await this.db
      .prepare('SELECT id, user_id, name, chapter, status, updated_at FROM series WHERE id = ? AND user_id = ?')
      .bind(id, userId)
      .first();

    if (!result) {
      return null;
    }

    return {
      id: result.id as string,
      userId: result.user_id as string,
      name: result.name as string,
      chapter: result.chapter as number | null,
      status: result.status as SeriesStatusType | null,
      updatedAt: result.updated_at as string,
    };
  }

  async createSeries(userId: string, input: CreateSeriesInput): Promise<Series> {
    const result = await this.db
      .prepare('INSERT INTO series (user_id, name, chapter, status) VALUES (?, ?, ?, ?) RETURNING id, user_id, name, chapter, status, updated_at')
      .bind(userId, input.name, input.chapter, input.status)
      .first();

    if (!result) {
      throw new Error('Failed to create series');
    }

    return {
      id: result.id as string,
      userId: result.user_id as string,
      name: result.name as string,
      chapter: result.chapter as number | null,
      status: result.status as SeriesStatusType | null,
      updatedAt: result.updated_at as string,
    };
  }

  async updateSeries(
    userId: string,
    id: string,
    input: UpdateSeriesInput
  ): Promise<Series | null> {
    if (Object.keys(input).length === 0) {
      return this.getSeriesById(userId, id);
    }

    // Build dynamic update query
    const updateFields: string[] = [];
    const params: any[] = [];

    if (input.name !== undefined) {
      updateFields.push('name = ?');
      params.push(input.name);
    }
    if (input.chapter !== undefined) {
      updateFields.push('chapter = ?');
      params.push(input.chapter);
    }
    if (input.status !== undefined) {
      updateFields.push('status = ?');
      params.push(input.status);
    }

    // Always update the timestamp
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id, userId); // Add id and userId for WHERE clause

    const query = `
      UPDATE series
      SET ${updateFields.join(', ')}
      WHERE id = ? AND user_id = ?
      RETURNING id, user_id, name, chapter, status, updated_at
    `;

    const result = await this.db.prepare(query).bind(...params).first();

    if (!result) {
      return null;
    }

    return {
      id: result.id as string,
      userId: result.user_id as string,
      name: result.name as string,
      chapter: result.chapter as number | null,
      status: result.status as SeriesStatusType | null,
      updatedAt: result.updated_at as string,
    };
  }

  async deleteSeries(userId: string, id: string): Promise<boolean> {
    // First check if the series exists for this user
    const existing = await this.db
      .prepare('SELECT id FROM series WHERE id = ? AND user_id = ?')
      .bind(id, userId)
      .first();

    if (!existing) {
      return false; // Series doesn't exist for this user
    }

    // Delete the series
    const result = await this.db
      .prepare('DELETE FROM series WHERE id = ? AND user_id = ?')
      .bind(id, userId)
      .run();

    return result.success && result.changes > 0;
  }

  async seriesExists(userId: string, name: string, excludeId?: string): Promise<boolean> {
    let query: string;
    let params: any[];

    if (excludeId) {
      query = 'SELECT 1 FROM series WHERE user_id = ? AND name = ? AND id != ?';
      params = [userId, name, excludeId];
    } else {
      query = 'SELECT 1 FROM series WHERE user_id = ? AND name = ?';
      params = [userId, name];
    }

    const result = await this.db.prepare(query).bind(...params).first();
    return !!result;
  }

  async getSeriesByName(userId: string, name: string): Promise<Series | null> {
    const result = await this.db
      .prepare('SELECT id, user_id, name, chapter, status, updated_at FROM series WHERE user_id = ? AND name = ? LIMIT 1')
      .bind(userId, name)
      .first();

    if (!result) {
      return null;
    }

    return {
      id: result.id as string,
      userId: result.user_id as string,
      name: result.name as string,
      chapter: result.chapter as number | null,
      status: result.status as SeriesStatusType | null,
      updatedAt: result.updated_at as string,
    };
  }
}
