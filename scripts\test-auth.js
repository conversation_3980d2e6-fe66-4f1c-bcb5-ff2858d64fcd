#!/usr/bin/env node

/**
 * Test script for JWKS-based JWT authentication
 * This script helps test the API with real OAuth provider tokens
 */

// Test configuration
const API_URL = process.env.API_URL || 'http://localhost:8787';
const JWT_TOKEN = process.env.JWT_TOKEN;
const USER_ID = process.env.USER_ID || 'test-user-123';

console.log('🔐 JWKS-based JWT Authentication Test');
console.log('=====================================\n');

if (!JWT_TOKEN) {
  console.log('❌ No JWT token provided!');
  console.log('\nTo test with a real JWT token:');
  console.log('1. Obtain a JWT token from your OAuth provider');
  console.log('2. Set the JWT_TOKEN environment variable:');
  console.log('   JWT_TOKEN=your-jwt-token npm run test-auth');
  console.log('\nExample with Auth0:');
  console.log('   JWT_TOKEN=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6... npm run test-auth');
  console.log('\nThe token should contain:');
  console.log('- sub: User ID (subject claim)');
  console.log('- iss: Issuer (your OAuth provider)');
  console.log('- aud: Audience (if configured)');
  console.log('- exp: Expiration time');
  console.log('- kid: Key ID (for JWKS lookup)');
  process.exit(1);
}

// Decode JWT payload (without verification, just for display)
function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }

    const header = JSON.parse(Buffer.from(parts[0], 'base64url').toString());
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());

    return { header, payload };
  } catch (error) {
    console.error('❌ Error decoding JWT:', error.message);
    return null;
  }
}

// Display token information
const decoded = decodeJWT(JWT_TOKEN);
if (decoded) {
  console.log('📋 JWT Token Information:');
  console.log('Header:', JSON.stringify(decoded.header, null, 2));
  console.log('Payload:', JSON.stringify(decoded.payload, null, 2));

  // Check for required claims
  console.log('\n🔍 Claim Validation:');
  console.log(`✓ Algorithm: ${decoded.header.alg}`);
  console.log(`✓ Key ID: ${decoded.header.kid || 'Not specified'}`);
  console.log(`✓ Subject: ${decoded.payload.sub || '❌ Missing'}`);
  console.log(`✓ Issuer: ${decoded.payload.iss || '❌ Missing'}`);
  console.log(`✓ Audience: ${decoded.payload.aud || 'Not specified'}`);

  if (decoded.payload.exp) {
    const expiry = new Date(decoded.payload.exp * 1000);
    const isExpired = Date.now() > decoded.payload.exp * 1000;
    console.log(`✓ Expires: ${expiry.toISOString()} ${isExpired ? '❌ EXPIRED' : '✅ Valid'}`);
  }
}

// Test API request
async function testAPI() {
  try {
    console.log('\n🚀 Testing API with JWT token...');
    console.log(`API URL: ${API_URL}`);

    const response = await fetch(`${API_URL}/api/series`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`\n📡 Response Status: ${response.status} ${response.statusText}`);

    const data = await response.json();
    console.log('Response Data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('\n✅ Authentication test successful!');
      console.log('🎉 Your JWKS-based authentication is working correctly!');
    } else {
      console.log('\n❌ Authentication test failed!');

      if (response.status === 401) {
        console.log('\n🔧 Troubleshooting tips:');
        console.log('1. Check that JWKS_URL is correctly configured');
        console.log('2. Verify the JWT token is valid and not expired');
        console.log('3. Ensure the token has the required "sub" claim');
        console.log('4. Check issuer/audience configuration if used');
      }
    }
  } catch (error) {
    console.error('\n❌ Error testing API:', error.message);
    console.log('\nMake sure:');
    console.log('1. Your API is running and accessible at:', API_URL);
    console.log('2. JWKS_URL is configured in your environment');
    console.log('3. Your OAuth provider\'s JWKS endpoint is accessible');
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testAPI();
}
