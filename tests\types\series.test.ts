import {
  SeriesStatus,
  SeriesSchema,
  CreateSeriesSchema,
  UpdateSeriesSchema,
  PaginationSchema,
} from '../../src/types/series';

describe('Series Types and Schemas', () => {
  describe('SeriesStatus', () => {
    it('should have all required status values', () => {
      expect(SeriesStatus.READING).toBe('Reading');
      expect(SeriesStatus.COMPLETED).toBe('Completed');
      expect(SeriesStatus.ON_HOLD).toBe('On-Hold');
      expect(SeriesStatus.DROPPED).toBe('Dropped');
      expect(SeriesStatus.CANCELLED).toBe('Cancelled');
      expect(SeriesStatus.PLAN_TO_READ).toBe('Plan to Read');
    });
  });

  describe('CreateSeriesSchema', () => {
    it('should validate valid series data', () => {
      const validData = {
        name: 'Test Series',
        chapter: 100,
        status: SeriesStatus.READING,
      };

      const result = CreateSeriesSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should validate series with minimal data', () => {
      const minimalData = {
        name: 'Test Series',
      };

      const result = CreateSeriesSchema.safeParse(minimalData);
      expect(result.success).toBe(true);
    });

    it('should reject empty name', () => {
      const invalidData = {
        name: '',
        chapter: 100,
      };

      const result = CreateSeriesSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject negative chapter', () => {
      const invalidData = {
        name: 'Test Series',
        chapter: -5,
      };

      const result = CreateSeriesSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid status', () => {
      const invalidData = {
        name: 'Test Series',
        status: 'Invalid Status',
      };

      const result = CreateSeriesSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should accept null values for optional fields', () => {
      const dataWithNulls = {
        name: 'Test Series',
        chapter: null,
        status: null,
      };

      const result = CreateSeriesSchema.safeParse(dataWithNulls);
      expect(result.success).toBe(true);
    });
  });

  describe('UpdateSeriesSchema', () => {
    it('should validate partial updates', () => {
      const partialData = {
        chapter: 150,
      };

      const result = UpdateSeriesSchema.safeParse(partialData);
      expect(result.success).toBe(true);
    });

    it('should validate empty update object', () => {
      const emptyData = {};

      const result = UpdateSeriesSchema.safeParse(emptyData);
      expect(result.success).toBe(true);
    });

    it('should reject empty name in update', () => {
      const invalidData = {
        name: '',
      };

      const result = UpdateSeriesSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('PaginationSchema', () => {
    it('should validate default pagination parameters', () => {
      const defaultParams = {};

      const result = PaginationSchema.safeParse(defaultParams);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.page).toBe(1);
        expect(result.data.pageSize).toBe(10);
        expect(result.data.sortBy).toBe('updatedAt');
        expect(result.data.sortOrder).toBe('desc');
      }
    });

    it('should validate custom pagination parameters', () => {
      const customParams = {
        page: '2',
        pageSize: '20',
        sortBy: 'name',
        sortOrder: 'asc',
        status: SeriesStatus.READING,
        search: 'test',
      };

      const result = PaginationSchema.safeParse(customParams);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.page).toBe(2);
        expect(result.data.pageSize).toBe(20);
        expect(result.data.sortBy).toBe('name');
        expect(result.data.sortOrder).toBe('asc');
        expect(result.data.status).toBe(SeriesStatus.READING);
        expect(result.data.search).toBe('test');
      }
    });

    it('should reject invalid page number', () => {
      const invalidParams = {
        page: '0',
      };

      const result = PaginationSchema.safeParse(invalidParams);
      expect(result.success).toBe(false);
    });

    it('should reject pageSize over 100', () => {
      const invalidParams = {
        pageSize: '101',
      };

      const result = PaginationSchema.safeParse(invalidParams);
      expect(result.success).toBe(false);
    });

    it('should reject invalid sortBy field', () => {
      const invalidParams = {
        sortBy: 'invalid_field',
      };

      const result = PaginationSchema.safeParse(invalidParams);
      expect(result.success).toBe(false);
    });

    it('should reject invalid sortOrder', () => {
      const invalidParams = {
        sortOrder: 'invalid_order',
      };

      const result = PaginationSchema.safeParse(invalidParams);
      expect(result.success).toBe(false);
    });
  });
});
