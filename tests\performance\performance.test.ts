import { CreateSeriesSchema, PaginationSchema, SeriesStatus } from '../../src/types/series';
import { DatabaseService } from '../../src/services/database';

// Mock the database service
jest.mock('../../src/services/database');

describe('Performance Tests', () => {
  describe('Schema Validation Performance', () => {
    it('should validate 1000 series schemas within 100ms', () => {
      const testData = Array.from({ length: 1000 }, (_, i) => ({
        name: `Performance Test Series ${i}`,
        chapter: i + 1,
        status: i % 2 === 0 ? SeriesStatus.READING : SeriesStatus.COMPLETED,
      }));

      const startTime = performance.now();
      
      const results = testData.map(data => CreateSeriesSchema.safeParse(data));
      
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(100); // Should complete within 100ms
      expect(results.every(result => result.success)).toBe(true);
    });

    it('should validate pagination schemas efficiently', () => {
      const testQueries = Array.from({ length: 500 }, (_, i) => ({
        page: String(i + 1),
        limit: String(Math.min(10 + (i % 90), 100)),
        sort: ['name', 'chapter', 'status', 'updated_at'][i % 4],
        order: i % 2 === 0 ? 'asc' : 'desc',
      }));

      const startTime = performance.now();
      
      const results = testQueries.map(query => PaginationSchema.safeParse(query));
      
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(50); // Should complete within 50ms
      expect(results.every(result => result.success)).toBe(true);
    });
  });

  describe('Memory Usage Tests', () => {
    it('should handle large datasets without memory leaks', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Create and validate a large number of objects
      for (let i = 0; i < 1000; i++) { // Reduced from 10000 to 1000 for faster testing
        const result = CreateSeriesSchema.safeParse({
          name: `Memory Test Series ${i}`,
          chapter: i + 1, // Ensure positive chapter numbers
          status: SeriesStatus.READING,
        });
        if (!result.success) {
          console.log('Validation failed for:', { name: `Memory Test Series ${i}`, chapter: i + 1, status: SeriesStatus.READING });
          console.log('Errors:', result.error.errors);
        }
        expect(result.success).toBe(true);
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    it('should efficiently handle concurrent validations', async () => {
      const concurrentValidations = Array.from({ length: 100 }, (_, i) =>
        Promise.resolve(CreateSeriesSchema.safeParse({
          name: `Concurrent Test ${i}`,
          chapter: i + 1, // Ensure positive chapter numbers
          status: SeriesStatus.READING,
        }))
      );

      const startTime = performance.now();
      const results = await Promise.all(concurrentValidations);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(10); // Should be very fast
      expect(results.every(result => result.success)).toBe(true);
    });
  });

  describe('Database Operation Performance', () => {
    let mockDatabaseService: jest.Mocked<DatabaseService>;

    beforeEach(() => {
      mockDatabaseService = {
        getAllSeries: jest.fn(),
        getSeriesById: jest.fn(),
        createSeries: jest.fn(),
        updateSeries: jest.fn(),
        deleteSeries: jest.fn(),
        seriesExists: jest.fn(),
      } as any;

      (DatabaseService as jest.Mock).mockImplementation(() => mockDatabaseService);
    });

    it('should handle rapid sequential database calls', async () => {
      // Mock fast database responses
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: [],
        totalCount: 0,
        page: 1,
        pageSize: 10,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      });

      const dbService = new DatabaseService('test://connection');
      const calls = Array.from({ length: 50 }, () =>
        dbService.getAllSeries({
          page: 1,
          pageSize: 10,
          sortBy: 'updatedAt',
          sortOrder: 'desc',
        })
      );

      const startTime = performance.now();
      await Promise.all(calls);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete quickly
      expect(mockDatabaseService.getAllSeries).toHaveBeenCalledTimes(50);
    });

    it('should efficiently handle large result sets', async () => {
      // Mock large dataset
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: `id-${i}`,
        name: `Series ${i}`,
        chapter: i,
        status: SeriesStatus.READING,
        updatedAt: new Date().toISOString(),
      }));

      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: largeDataset,
        totalCount: 1000,
        page: 1,
        pageSize: 1000,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      });

      const dbService = new DatabaseService('test://connection');

      const startTime = performance.now();
      const result = await dbService.getAllSeries({
        page: 1,
        pageSize: 1000,
        sortBy: 'updatedAt',
        sortOrder: 'desc',
      });
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(50); // Should handle large datasets quickly
      expect(result.data).toHaveLength(1000);
    });
  });

  describe('Stress Tests', () => {
    it('should handle validation errors gracefully under load', () => {
      const invalidData = Array.from({ length: 1000 }, (_, i) => ({
        name: '', // Invalid empty name
        chapter: -i, // Invalid negative chapter
        status: 'InvalidStatus', // Invalid status
      }));

      const startTime = performance.now();
      
      const results = invalidData.map(data => CreateSeriesSchema.safeParse(data));
      
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(200); // Should handle errors efficiently
      expect(results.every(result => !result.success)).toBe(true);
    });

    it('should maintain performance with mixed valid/invalid data', () => {
      const mixedData = Array.from({ length: 2000 }, (_, i) => ({
        name: i % 2 === 0 ? `Valid Series ${i}` : '', // 50% invalid names
        chapter: i % 3 === 0 ? i : -1, // 33% invalid chapters
        status: i % 4 === 0 ? SeriesStatus.READING : 'InvalidStatus', // 25% invalid status
      }));

      const startTime = performance.now();
      
      const results = mixedData.map(data => CreateSeriesSchema.safeParse(data));
      
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(300); // Should handle mixed data efficiently
      
      const validResults = results.filter(result => result.success);
      const invalidResults = results.filter(result => !result.success);
      
      expect(validResults.length).toBeGreaterThan(0);
      expect(invalidResults.length).toBeGreaterThan(0);
    });
  });

  describe('Resource Cleanup Tests', () => {
    it('should properly clean up resources after operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform multiple operations
      for (let i = 0; i < 100; i++) {
        CreateSeriesSchema.safeParse({
          name: `Cleanup Test ${i}`,
          chapter: i,
          status: SeriesStatus.READING,
        });
      }

      // Allow some time for cleanup
      await new Promise(resolve => setTimeout(resolve, 10));

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Should not have significant memory increase (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });
  });
});
