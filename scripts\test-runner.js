#!/usr/bin/env node

/**
 * Advanced Test Runner Script
 * Provides enhanced testing capabilities with detailed reporting
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      coverage: null,
    };
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // Cyan
      success: '\x1b[32m', // Green
      warning: '\x1b[33m', // Yellow
      error: '\x1b[31m',   // Red
      reset: '\x1b[0m',    // Reset
    };

    const timestamp = new Date().toISOString();
    console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
  }

  async runCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
      const child = spawn('npm', ['run', command], {
        stdio: 'pipe',
        shell: true,
        ...options,
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
        if (options.verbose) {
          process.stdout.write(data);
        }
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
        if (options.verbose) {
          process.stderr.write(data);
        }
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr, code });
        } else {
          reject({ stdout, stderr, code });
        }
      });
    });
  }

  parseJestOutput(output) {
    const lines = output.split('\n');
    const results = {
      testSuites: 0,
      tests: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
    };

    lines.forEach(line => {
      if (line.includes('Test Suites:')) {
        const match = line.match(/(\d+) passed.*?(\d+) total/);
        if (match) {
          results.testSuites = parseInt(match[2]);
        }
      }
      
      if (line.includes('Tests:')) {
        const passedMatch = line.match(/(\d+) passed/);
        const failedMatch = line.match(/(\d+) failed/);
        const skippedMatch = line.match(/(\d+) skipped/);
        const totalMatch = line.match(/(\d+) total/);
        
        if (passedMatch) results.passed = parseInt(passedMatch[1]);
        if (failedMatch) results.failed = parseInt(failedMatch[1]);
        if (skippedMatch) results.skipped = parseInt(skippedMatch[1]);
        if (totalMatch) results.tests = parseInt(totalMatch[1]);
      }
      
      if (line.includes('Time:')) {
        const timeMatch = line.match(/Time:\s*([\d.]+)\s*s/);
        if (timeMatch) {
          results.duration = parseFloat(timeMatch[1]);
        }
      }
    });

    return results;
  }

  async runTestSuite(suiteName, command, description) {
    this.log(`Starting ${description}...`, 'info');
    const startTime = Date.now();

    try {
      const result = await this.runCommand(command, { verbose: false });
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;

      const parsed = this.parseJestOutput(result.stdout);
      
      this.log(`✅ ${description} completed in ${duration.toFixed(2)}s`, 'success');
      this.log(`   Tests: ${parsed.passed} passed, ${parsed.failed} failed, ${parsed.skipped} skipped`, 'info');
      
      return {
        name: suiteName,
        success: true,
        duration,
        ...parsed,
      };
    } catch (error) {
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      
      this.log(`❌ ${description} failed in ${duration.toFixed(2)}s`, 'error');
      this.log(`   Error: ${error.stderr || error.stdout}`, 'error');
      
      return {
        name: suiteName,
        success: false,
        duration,
        error: error.stderr || error.stdout,
      };
    }
  }

  async runCoverageReport() {
    this.log('Generating coverage report...', 'info');
    
    try {
      const result = await this.runCommand('test:coverage', { verbose: false });
      
      // Parse coverage from output
      const coverageMatch = result.stdout.match(/All files\s*\|\s*([\d.]+)/);
      const coverage = coverageMatch ? parseFloat(coverageMatch[1]) : 0;
      
      this.log(`📊 Code coverage: ${coverage}%`, coverage >= 80 ? 'success' : 'warning');
      
      return { coverage, success: true };
    } catch (error) {
      this.log('❌ Coverage report failed', 'error');
      return { coverage: 0, success: false, error: error.stderr };
    }
  }

  async runPerformanceTests() {
    this.log('Running performance tests...', 'info');
    
    try {
      const result = await this.runCommand('test', { verbose: false });
      
      // Look for performance warnings in output
      const slowTests = result.stdout.match(/Slow test detected: ([\d.]+)ms/g) || [];
      
      if (slowTests.length > 0) {
        this.log(`⚠️  Found ${slowTests.length} slow tests`, 'warning');
        slowTests.forEach(test => this.log(`   ${test}`, 'warning'));
      } else {
        this.log('✅ All tests performed within acceptable limits', 'success');
      }
      
      return { slowTests: slowTests.length, success: true };
    } catch (error) {
      this.log('❌ Performance tests failed', 'error');
      return { slowTests: 0, success: false, error: error.stderr };
    }
  }

  generateReport(results) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalSuites: results.length,
        passedSuites: results.filter(r => r.success).length,
        failedSuites: results.filter(r => !r.success).length,
        totalDuration: results.reduce((sum, r) => sum + r.duration, 0),
        totalTests: results.reduce((sum, r) => sum + (r.tests || 0), 0),
        totalPassed: results.reduce((sum, r) => sum + (r.passed || 0), 0),
        totalFailed: results.reduce((sum, r) => sum + (r.failed || 0), 0),
      },
      suites: results,
    };

    // Write report to file
    const reportPath = path.join(process.cwd(), 'test-results.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.log(`📄 Test report saved to ${reportPath}`, 'info');
    
    return report;
  }

  printSummary(report) {
    this.log('\n🧪 TEST SUMMARY', 'info');
    this.log('================', 'info');
    this.log(`Total Test Suites: ${report.summary.totalSuites}`, 'info');
    this.log(`Passed: ${report.summary.passedSuites}`, 'success');
    this.log(`Failed: ${report.summary.failedSuites}`, report.summary.failedSuites > 0 ? 'error' : 'info');
    this.log(`Total Tests: ${report.summary.totalTests}`, 'info');
    this.log(`Passed: ${report.summary.totalPassed}`, 'success');
    this.log(`Failed: ${report.summary.totalFailed}`, report.summary.totalFailed > 0 ? 'error' : 'info');
    this.log(`Total Duration: ${report.summary.totalDuration.toFixed(2)}s`, 'info');
    
    if (report.summary.failedSuites > 0) {
      this.log('\n❌ FAILED SUITES:', 'error');
      report.suites.filter(s => !s.success).forEach(suite => {
        this.log(`   ${suite.name}: ${suite.error}`, 'error');
      });
    }
    
    this.log('\n🎉 Testing completed!', report.summary.failedSuites === 0 ? 'success' : 'warning');
  }

  async run() {
    this.log('🚀 Starting comprehensive test suite...', 'info');
    
    const testSuites = [
      {
        name: 'types',
        command: 'test:unit',
        description: 'Type and Schema Validation Tests',
      },
      {
        name: 'performance',
        command: 'test',
        description: 'Performance Tests',
      },
      {
        name: 'integration',
        command: 'test:integration',
        description: 'Integration Tests',
      },
    ];

    const results = [];
    
    // Run each test suite
    for (const suite of testSuites) {
      const result = await this.runTestSuite(suite.name, suite.command, suite.description);
      results.push(result);
      
      // Short pause between suites
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Run coverage report
    const coverageResult = await this.runCoverageReport();
    results.push({
      name: 'coverage',
      success: coverageResult.success,
      coverage: coverageResult.coverage,
      duration: 0,
    });

    // Generate and display report
    const report = this.generateReport(results);
    this.printSummary(report);
    
    // Exit with appropriate code
    process.exit(report.summary.failedSuites > 0 ? 1 : 0);
  }
}

// CLI interface
const args = process.argv.slice(2);
const command = args[0];

const runner = new TestRunner();

switch (command) {
  case 'all':
  case undefined:
    runner.run();
    break;
  case 'coverage':
    runner.runCoverageReport();
    break;
  case 'performance':
    runner.runPerformanceTests();
    break;
  default:
    console.log('Usage: node scripts/test-runner.js [all|coverage|performance]');
    process.exit(1);
}
