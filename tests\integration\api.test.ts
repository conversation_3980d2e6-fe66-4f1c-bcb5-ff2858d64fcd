import { SeriesStatus } from '../../src/types/series';
import app from '../../src/index';

// Mock the database service for integration tests
jest.mock('../../src/services/database');

// Mock environment variables
const mockEnv = {
  DB: {
    prepare: jest.fn().mockReturnThis(),
    bind: jest.fn().mockReturnThis(),
    first: jest.fn(),
    all: jest.fn(),
    run: jest.fn(),
  } as any,
  JWKS_URL: 'https://test-provider.com/.well-known/jwks.json',
  JWT_ISSUER: 'test-issuer',
  JWT_AUDIENCE: 'test-audience',
};

// Helper function to make requests to Hono app
async function makeRequest(method: string, path: string, body?: any, includeAuth: boolean = true) {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add mock JWT token for authenticated requests
  if (includeAuth) {
    headers['Authorization'] = 'Bearer mock-jwt-token';
  }

  const request = new Request(`http://localhost${path}`, {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined,
  });

  return await app.fetch(request, mockEnv);
}

// Helper function to get JSON data with proper typing
async function getJsonData(response: Response): Promise<any> {
  return await response.json();
}

describe('API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Health Check', () => {
    it('should return API information', async () => {
      const response = await makeRequest('GET', '/', undefined, false); // No auth for health check
      const data = await getJsonData(response);

      expect(response.status).toBe(200);
      expect(data).toEqual({
        message: 'Series API is running',
        version: '1.0.0',
        endpoints: {
          'GET /api/series': 'List all series with pagination',
          'GET /api/series/:id': 'Get series by ID',
          'POST /api/series': 'Create new series',
          'PUT /api/series/:id': 'Update series',
          'DELETE /api/series/:id': 'Delete series',
          'GET /api/statuses': 'Get available series statuses',
        },
      });
    });
  });

  // Note: CORS functionality is configured in the main app but may not be testable in this environment

  describe('404 Handler', () => {
    it('should return 404 for non-existent endpoints', async () => {
      const response = await makeRequest('GET', '/non-existent-endpoint');
      const data = await getJsonData(response);

      expect(response.status).toBe(404);
      expect(data).toEqual({
        success: false,
        error: 'Endpoint not found',
        message: 'The requested endpoint does not exist',
      });
    });
  });

  describe('Error Handler', () => {
    it('should handle internal server errors gracefully', async () => {
      // Mock the database service to throw an error
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getAllSeries: jest.fn().mockRejectedValue(new Error('Database connection failed')),
      }));

      const response = await makeRequest('GET', '/api/series');
      const data = await getJsonData(response);

      expect(response.status).toBe(500);
      expect(data).toEqual({
        success: false,
        error: 'Internal server error',
      });
    });
  });

  describe('Request Validation', () => {
    it('should validate pagination parameters', async () => {
      const response = await makeRequest('GET', '/api/series?page=0&pageSize=101');
      const data = await getJsonData(response);

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid query parameters');
    });

    it('should validate create series request body', async () => {
      const response = await makeRequest('POST', '/api/series', {
        name: '', // Invalid empty name
        chapter: -5, // Invalid negative chapter
      });
      const data = await getJsonData(response);

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid request data');
    });

    it('should validate update series request body', async () => {
      const response = await makeRequest('PUT', '/api/series/123e4567-e89b-12d3-a456-426614174000', {
        name: '', // Invalid empty name
      });
      const data = await getJsonData(response);

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid request data');
    });
  });

  describe('Content-Type Handling', () => {
    it('should require JSON content type for POST requests', async () => {
      const request = new Request('http://localhost/api/series', {
        method: 'POST',
        headers: {
          'Content-Type': 'text/plain',
        },
        body: 'invalid-json',
      });

      const response = await app.fetch(request, mockEnv);
      const data = await getJsonData(response);

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
    });

    it('should accept valid JSON for POST requests', async () => {
      // Mock successful creation
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        seriesExists: jest.fn().mockResolvedValue(false),
        getSeriesByName: jest.fn().mockResolvedValue(null),
        createSeries: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 1,
          status: 'Reading',
          updatedAt: '2024-01-01T00:00:00.000Z',
        }),
      }));

      const response = await makeRequest('POST', '/api/series', {
        name: 'Test Series',
        chapter: 1,
        status: SeriesStatus.READING,
      });
      const data = await getJsonData(response);

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.name).toBe('Test Series');
    });
  });

  describe('HTTP Methods', () => {
    it('should support GET method for listing series', async () => {
      // Mock successful retrieval
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getAllSeries: jest.fn().mockResolvedValue({
          data: [],
          totalCount: 0,
          page: 1,
          pageSize: 10,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        }),
      }));

      const response = await makeRequest('GET', '/api/series');
      const data = await getJsonData(response);

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.data).toEqual([]);
    });

    it('should support GET method for single series', async () => {
      // Mock successful retrieval
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getSeriesById: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 100,
          status: 'Reading',
          updatedAt: '2024-01-01T00:00:00.000Z',
        }),
      }));

      const response = await makeRequest('GET', '/api/series/123e4567-e89b-12d3-a456-426614174000');
      const data = await getJsonData(response);

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.name).toBe('Test Series');
    });

    it('should support PUT method for updating series', async () => {
      // Mock successful update
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getSeriesById: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 100,
          status: 'Reading',
          updatedAt: '2024-01-01T00:00:00.000Z',
        }),
        seriesExists: jest.fn().mockResolvedValue(false),
        updateSeries: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Updated Series',
          chapter: 150,
          status: 'Completed',
          updatedAt: '2024-01-02T00:00:00.000Z',
        }),
      }));

      const response = await makeRequest('PUT', '/api/series/123e4567-e89b-12d3-a456-426614174000', {
        name: 'Updated Series',
        chapter: 150,
        status: SeriesStatus.COMPLETED,
      });
      const data = await getJsonData(response);

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.name).toBe('Updated Series');
    });

    it('should support DELETE method for deleting series', async () => {
      // Mock successful deletion
      const { DatabaseService } = require('../../src/services/database');
      DatabaseService.mockImplementation(() => ({
        getSeriesById: jest.fn().mockResolvedValue({
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 100,
          status: 'Reading',
          updatedAt: '2024-01-01T00:00:00.000Z',
        }),
        deleteSeries: jest.fn().mockResolvedValue(true),
      }));

      const response = await makeRequest('DELETE', '/api/series/123e4567-e89b-12d3-a456-426614174000');
      const data = await getJsonData(response);

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Series deleted successfully');
    });
  });

  describe('Query Parameter Handling', () => {
    it('should handle pagination query parameters', async () => {
      // Mock successful retrieval with pagination
      const { DatabaseService } = require('../../src/services/database');
      const mockGetAllSeries = jest.fn().mockResolvedValue({
        data: [],
        totalCount: 20,
        page: 2,
        pageSize: 5,
        totalPages: 4,
        hasNextPage: true,
        hasPreviousPage: true,
      });

      DatabaseService.mockImplementation(() => ({
        getAllSeries: mockGetAllSeries,
      }));

      const response = await makeRequest('GET', '/api/series?page=2&pageSize=5&sortBy=name&sortOrder=asc');

      expect(response.status).toBe(200);
      expect(mockGetAllSeries).toHaveBeenCalledWith({
        page: 2,
        pageSize: 5,
        sortBy: 'name',
        sortOrder: 'asc',
      });
    });

    it('should handle filter query parameters', async () => {
      // Mock successful retrieval with filters
      const { DatabaseService } = require('../../src/services/database');
      const mockGetAllSeries = jest.fn().mockResolvedValue({
        data: [],
        totalCount: 5,
        page: 1,
        pageSize: 10,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      });

      DatabaseService.mockImplementation(() => ({
        getAllSeries: mockGetAllSeries,
      }));

      const response = await makeRequest('GET', '/api/series?status=Reading&search=naruto');

      expect(response.status).toBe(200);
      expect(mockGetAllSeries).toHaveBeenCalledWith({
        page: 1,
        pageSize: 10,
        sortBy: 'updatedAt',
        sortOrder: 'desc',
        status: 'Reading',
        search: 'naruto',
      });
    });
  });

  describe('Statuses Endpoint', () => {
    it('should return available series statuses', async () => {
      const response = await makeRequest('GET', '/api/statuses');
      const data = await getJsonData(response);

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual([
        'Reading',
        'Completed',
        'On-Hold',
        'Dropped',
        'Cancelled',
        'Plan to Read',
      ]);
    });
  });
});
