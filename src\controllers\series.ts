import { Context } from "hono";
import { DatabaseService } from "../services/database";
import { getUserContext } from "../middleware/auth";
import {
  CreateSeriesSchema,
  UpdateSeriesSchema,
  PaginationSchema,
  ApiResponse,
  Series,
  PaginatedResponse,
} from "../types/series";
import { D1Database } from "@cloudflare/workers-types";

type CloudflareBindings = {
  DB: D1Database;
  JWKS_URL: string;
  JWT_ISSUER?: string;
  JWT_AUDIENCE?: string;
};

export class SeriesController {
  private getDbService(
    c: Context<{ Bindings: CloudflareBindings }>
  ): DatabaseService {
    return new DatabaseService(c.env.DB);
  }

  async getAllSeries(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      // Get user context from authenticated request
      const user = getUserContext(c);

      // Validate query parameters
      const queryResult = PaginationSchema.safeParse({
        page: c.req.query("page"),
        pageSize: c.req.query("pageSize"),
        sortBy: c.req.query("sortBy"),
        sortOrder: c.req.query("sortOrder"),
        status: c.req.query("status"),
        search: c.req.query("search"),
      });

      if (!queryResult.success) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Invalid query parameters",
            data: null,
          },
          400
        );
      }

      const db = this.getDbService(c);
      const result = await db.getAllSeries(user.userId, queryResult.data);

      return c.json<ApiResponse<PaginatedResponse<Series>>>({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error("Error fetching series:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }

  async getSeriesById(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      // Get user context from authenticated request
      const user = getUserContext(c);
      const id = c.req.param("id");

      if (!id) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series ID is required",
          },
          400
        );
      }

      const db = this.getDbService(c);
      const series = await db.getSeriesById(user.userId, id);

      if (!series) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series not found",
          },
          404
        );
      }

      return c.json<ApiResponse<Series>>({
        success: true,
        data: series,
      });
    } catch (error) {
      console.error("Error fetching series by ID:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }

  async createSeries(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      // Get user context from authenticated request
      const user = getUserContext(c);
      const body = await c.req.json();

      // Validate request body
      const validationResult = CreateSeriesSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            success: false,
            error: "Invalid request data",
            details: validationResult.error.errors,
          },
          400
        );
      }

      const db = this.getDbService(c);

      // Check if series with same name already exists for this user
      const exists = await db.seriesExists(user.userId, validationResult.data.name);
      if (exists) {
        // Get the existing series for conflict response
        const existingSeries = await db.getSeriesByName(user.userId, validationResult.data.name);
        return c.json(
          {
            success: false,
            error: "Series with this name already exists",
            existing: existingSeries,
            proposed: validationResult.data,
          },
          409
        );
      }

      const series = await db.createSeries(user.userId, validationResult.data);

      return c.json<ApiResponse<Series>>(
        {
          success: true,
          data: series,
          message: "Series created successfully",
        },
        201
      );
    } catch (error) {
      console.error("Error creating series:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }

  async updateSeries(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      // Get user context from authenticated request
      const user = getUserContext(c);
      const id = c.req.param("id");
      const body = await c.req.json();

      if (!id) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series ID is required",
          },
          400
        );
      }

      // Validate request body
      const validationResult = UpdateSeriesSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            success: false,
            error: "Invalid request data",
            details: validationResult.error.errors,
          },
          400
        );
      }

      const db = this.getDbService(c);

      // Check if series exists for this user
      const existingSeries = await db.getSeriesById(user.userId, id);
      if (!existingSeries) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series not found",
          },
          404
        );
      }

      // Check if name is being updated and if it conflicts with existing series for this user
      if (validationResult.data.name) {
        const nameExists = await db.seriesExists(
          user.userId,
          validationResult.data.name,
          id
        );
        if (nameExists) {
          const conflictingSeries = await db.getSeriesByName(user.userId, validationResult.data.name);
          return c.json(
            {
              success: false,
              error: "Series with this name already exists",
              existing: conflictingSeries,
              proposed: { ...existingSeries, ...validationResult.data },
            },
            409
          );
        }
      }

      const updatedSeries = await db.updateSeries(user.userId, id, validationResult.data);

      return c.json<ApiResponse<Series>>({
        success: true,
        data: updatedSeries!,
        message: "Series updated successfully",
      });
    } catch (error) {
      console.error("Error updating series:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }

  async deleteSeries(c: Context<{ Bindings: CloudflareBindings }>) {
    try {
      // Get user context from authenticated request
      const user = getUserContext(c);
      const id = c.req.param("id");

      if (!id) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series ID is required",
          },
          400
        );
      }

      const db = this.getDbService(c);

      // Check if series exists for this user
      const existingSeries = await db.getSeriesById(user.userId, id);
      if (!existingSeries) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Series not found",
          },
          404
        );
      }

      const deleted = await db.deleteSeries(user.userId, id);

      if (!deleted) {
        return c.json<ApiResponse<null>>(
          {
            success: false,
            error: "Failed to delete series",
          },
          500
        );
      }

      return c.json<ApiResponse<null>>({
        success: true,
        message: "Series deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting series:", error);
      return c.json<ApiResponse<null>>(
        {
          success: false,
          error: "Internal server error",
        },
        500
      );
    }
  }
}
