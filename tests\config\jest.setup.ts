// Enhanced Jest setup for comprehensive testing

// Environment setup
process.env.NODE_ENV = 'test';
// D1 database will be mocked in individual test files - no environment variables needed

// Global test configuration
jest.setTimeout(30000); // 30 seconds for complex tests

// Mock global objects
global.fetch = jest.fn();
global.console = {
  ...console,
  // Suppress console.log in tests unless explicitly needed
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Performance monitoring
const originalPerformanceNow = performance.now;
let testStartTime: number;

beforeEach(() => {
  testStartTime = originalPerformanceNow.call(performance);
  jest.clearAllMocks();
});

afterEach(() => {
  const testEndTime = originalPerformanceNow.call(performance);
  const testDuration = testEndTime - testStartTime;
  
  // Log slow tests (over 1 second)
  if (testDuration > 1000) {
    console.warn(`Slow test detected: ${testDuration.toFixed(2)}ms`);
  }
});

// Memory leak detection
let initialMemoryUsage: NodeJS.MemoryUsage;

beforeAll(() => {
  initialMemoryUsage = process.memoryUsage();
});

afterAll(() => {
  const finalMemoryUsage = process.memoryUsage();
  const memoryIncrease = finalMemoryUsage.heapUsed - initialMemoryUsage.heapUsed;
  
  // Warn about potential memory leaks (over 50MB increase)
  if (memoryIncrease > 50 * 1024 * 1024) {
    console.warn(`Potential memory leak detected: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB increase`);
  }
});

// Global test utilities
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidUUID(): R;
      toBeValidDate(): R;
      toHaveValidPaginationStructure(): R;
      toBeWithinTimeRange(start: Date, end: Date): R;
    }
  }
}

// Custom Jest matchers
expect.extend({
  toBeValidUUID(received: string) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = typeof received === 'string' && uuidRegex.test(received);
    
    return {
      message: () => `expected ${received} to be a valid UUID`,
      pass,
    };
  },

  toBeValidDate(received: any) {
    const pass = received instanceof Date && !isNaN(received.getTime());
    
    return {
      message: () => `expected ${received} to be a valid Date object`,
      pass,
    };
  },

  toHaveValidPaginationStructure(received: any) {
    const requiredFields = ['page', 'limit', 'total', 'totalPages', 'hasNext', 'hasPrev'];
    const hasAllFields = requiredFields.every(field => field in received);
    const hasCorrectTypes = 
      typeof received.page === 'number' &&
      typeof received.limit === 'number' &&
      typeof received.total === 'number' &&
      typeof received.totalPages === 'number' &&
      typeof received.hasNext === 'boolean' &&
      typeof received.hasPrev === 'boolean';
    
    const pass = hasAllFields && hasCorrectTypes;
    
    return {
      message: () => `expected ${JSON.stringify(received)} to have valid pagination structure`,
      pass,
    };
  },

  toBeWithinTimeRange(received: Date, start: Date, end: Date) {
    const pass = received >= start && received <= end;
    
    return {
      message: () => `expected ${received.toISOString()} to be between ${start.toISOString()} and ${end.toISOString()}`,
      pass,
    };
  },
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Test database cleanup utilities
export const TestCleanup = {
  async clearTestData() {
    // In a real scenario, this would clear test data from the database
    // For now, we just reset mocks
    jest.clearAllMocks();
  },

  async resetDatabase() {
    // In a real scenario, this would reset the test database to a clean state
    console.log('Database reset (mocked)');
  },
};

// Test performance utilities
export const TestPerformance = {
  measureExecutionTime: async <T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> => {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    return { result, duration: end - start };
  },

  expectFastExecution: (duration: number, maxMs: number = 100) => {
    expect(duration).toBeLessThan(maxMs);
  },

  measureMemoryUsage: <T>(fn: () => T): { result: T; memoryDelta: number } => {
    const initialMemory = process.memoryUsage().heapUsed;
    const result = fn();
    const finalMemory = process.memoryUsage().heapUsed;
    return { result, memoryDelta: finalMemory - initialMemory };
  },
};

// Test data validation utilities
export const TestValidation = {
  validateApiResponse: (response: any) => {
    expect(response).toHaveProperty('success');
    expect(typeof response.success).toBe('boolean');
    
    if (response.success) {
      expect(response).toHaveProperty('data');
    } else {
      expect(response).toHaveProperty('error');
      expect(typeof response.error).toBe('string');
    }
  },

  validateSeriesObject: (series: any) => {
    expect(series).toHaveProperty('id');
    expect(series).toHaveProperty('name');
    expect(series).toHaveProperty('chapter');
    expect(series).toHaveProperty('status');
    expect(series).toHaveProperty('updated_at');
    
    expect(series.id).toBeValidUUID();
    expect(typeof series.name).toBe('string');
    expect(series.chapter === null || typeof series.chapter === 'number').toBe(true);
    expect(series.status === null || typeof series.status === 'string').toBe(true);
    expect(new Date(series.updated_at)).toBeValidDate();
  },

  validatePaginatedResponse: (response: any) => {
    TestValidation.validateApiResponse(response);
    expect(response.data).toHaveProperty('data');
    expect(response.data).toHaveProperty('pagination');
    expect(Array.isArray(response.data.data)).toBe(true);
    expect(response.data.pagination).toHaveValidPaginationStructure();
  },
};

// Test assertion helpers
export const TestAssertions = {
  expectValidationError: (result: any, field?: string) => {
    expect(result.success).toBe(false);
    if (field) {
      expect(result.error.errors.some((err: any) => err.path.includes(field))).toBe(true);
    }
  },

  expectValidationSuccess: (result: any, expectedData?: any) => {
    expect(result.success).toBe(true);
    if (expectedData) {
      expect(result.data).toEqual(expectedData);
    }
  },

  expectHttpStatus: (response: any, expectedStatus: number) => {
    expect(response.status).toBe(expectedStatus);
  },

  expectErrorResponse: (response: any, expectedError?: string) => {
    expect(response.body.success).toBe(false);
    if (expectedError) {
      expect(response.body.error).toContain(expectedError);
    }
  },

  expectSuccessResponse: (response: any, expectedData?: any) => {
    expect(response.body.success).toBe(true);
    if (expectedData) {
      expect(response.body.data).toEqual(expectedData);
    }
  },
};
